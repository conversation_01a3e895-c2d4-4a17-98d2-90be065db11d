<!-- fragment_exposurecorrection.xml -->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:orientation="vertical"
        android:padding="5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Rozbalovací karta pro korekci hustoty -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            app:cardBackgroundColor="#424242"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> karty s tlačítkem pro rozbalení/sbalení -->
                <LinearLayout
                    android:id="@+id/cardHeaderDensity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?android:attr/selectableItemBackground"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📊 Korekce podle hustoty"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white" />

                    <ImageView
                        android:id="@+id/iconExpandDensity"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_expand_more"
                        app:tint="@android:color/white"
                        android:contentDescription="Rozbalit/sbalit" />

                </LinearLayout>

                <!-- Obsah karty (rozbalovací) - defaultně sbalený -->
                <LinearLayout
                    android:id="@+id/cardContentDensity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <!-- Název vzorce -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/exposure_density_title"
                        android:textStyle="bold"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:textSize="16sp"
                        android:textColor="@android:color/white"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="4dp"/>

                    <!-- Vzorec -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/formula_density_new"
                        android:textStyle="italic"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:textColor="@android:color/white"
                        android:layout_marginBottom="8dp"/>

                    <!-- Výpočetní pole -->
                    <LinearLayout
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:id="@+id/textExposure2"
                            android:layout_width="60dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:hint="@string/hint_exp2"
                            style="@style/Text.Result"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" = "
                            android:textSize="18sp"
                            android:textColor="@android:color/white"/>

                        <EditText
                            android:id="@+id/inputExposure1"
                            android:layout_width="60dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:inputType="numberDecimal"
                            android:hint="@string/hint_exp1"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" × ("
                            android:textSize="18sp"
                            android:textColor="@android:color/white"/>

                        <EditText
                            android:id="@+id/inputDensity2"
                            android:layout_width="70dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:inputType="numberDecimal"
                            android:hint="@string/hint_dens2"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="/"
                            android:textSize="18sp"
                            android:textColor="@android:color/white"/>

                        <EditText
                            android:id="@+id/inputDensity1"
                            android:layout_width="70dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:inputType="numberDecimal"
                            android:hint="@string/hint_dens1"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" )"
                            android:textSize="18sp"
                            android:textColor="@android:color/white"/>
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Rozbalovací karta pro korekci vzdálenosti -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="#424242"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- Hlavička karty s tlačítkem pro rozbalení/sbalení -->
                <LinearLayout
                    android:id="@+id/cardHeaderDistance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?android:attr/selectableItemBackground"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📏 Korekce podle vzdálenosti"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white" />

                    <ImageView
                        android:id="@+id/iconExpandDistance"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_expand_more"
                        app:tint="@android:color/white"
                        android:contentDescription="Rozbalit/sbalit" />

                </LinearLayout>

                <!-- Obsah karty (rozbalovací) - defaultně sbalený -->
                <LinearLayout
                    android:id="@+id/cardContentDistance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <!-- Název vzorce -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/distance_correction_title"
                        android:textStyle="bold"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:textSize="16sp"
                        android:textColor="@android:color/white"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="4dp"/>

                    <!-- Vzorec -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/formula_distance_new"
                        android:textStyle="italic"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:textColor="@android:color/white"
                        android:layout_marginBottom="8dp"/>

                    <!-- Výpočetní pole -->
                    <LinearLayout
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:id="@+id/textExposure2Dist"
                            android:layout_width="60dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:hint="@string/hint_exp2"
                            style="@style/Text.Result"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" = "
                            android:textSize="18sp"
                            android:textColor="@android:color/white"/>

                        <EditText
                            android:id="@+id/inputExposure1Dist"
                            android:layout_width="60dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:inputType="numberDecimal"
                            android:hint="@string/hint_exp1"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" × ("
                            android:textSize="18sp"
                            android:textColor="@android:color/white"/>

                        <EditText
                            android:id="@+id/inputDistance2"
                            android:layout_width="70dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:inputType="numberDecimal"
                            android:hint="@string/hint_dist2"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="/"
                            android:textSize="18sp"
                            android:textColor="@android:color/white"/>

                        <EditText
                            android:id="@+id/inputDistance1"
                            android:layout_width="70dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:inputType="numberDecimal"
                            android:hint="@string/hint_dist1"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" )²"
                            android:textSize="18sp"
                            android:textColor="@android:color/white"/>
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView>