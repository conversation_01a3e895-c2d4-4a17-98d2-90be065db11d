<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Rozbalovací karta pro nastavení zdroje -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="#424242"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> karty s tlačítkem pro rozbalení/sbalení -->
                <LinearLayout
                    android:id="@+id/cardHeaderSource"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?android:attr/selectableItemBackground"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="⚛️ Nastavení izotopu a máterialu"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white" />

                    <ImageView
                        android:id="@+id/iconExpandSource"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_expand_more"
                        android:tint="@android:color/white"
                        android:contentDescription="Rozbalit/sbalit" />

                </LinearLayout>

                <!-- Obsah karty (rozbalovací) -->
                <LinearLayout
                    android:id="@+id/cardContentSource"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <!-- Typ zářiče / Počáteční aktivita / Počáteční datum -->
                    <LinearLayout
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="4dp">
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_isotope_type"
                            android:textColor="@android:color/white"
                            style="@style/Text.Info" />
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_initial_activity"
                            android:gravity="center"
                            android:textColor="@android:color/white"
                            style="@style/Text.Info"
                            android:layout_marginStart="8dp"/>
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_initial_date"
                            android:gravity="center"
                            android:textColor="@android:color/white"
                            style="@style/Text.Info"
                            android:layout_marginStart="8dp"/>
                    </LinearLayout>

                    <LinearLayout
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp">
                        <Spinner
                            android:id="@+id/spinnerIsotope"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"/>
                        <EditText
                            android:id="@+id/inputInitialActivity"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:inputType="numberDecimal"
                            android:gravity="center"
                            style="@style/Text.Input"
                            android:layout_marginStart="8dp"/>
                        <EditText
                            android:id="@+id/inputInitialDate"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:focusable="false"
                            android:clickable="true"
                            style="@style/Text.Input"
                            android:layout_marginStart="8dp"/>
                    </LinearLayout>

                    <LinearLayout
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp">

                        <!-- Aktivita -->
                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_activity"
                            android:gravity="center"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white"
                            style="@style/Text.Info"
                            android:layout_marginBottom="4dp"/>
                        <EditText
                            android:id="@+id/inputActivity"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:inputType="numberDecimal"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:layout_marginBottom="12dp"
                            style="@style/Text.Input"/>
                    </LinearLayout>

                    <LinearLayout
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp">

                        <!-- Materiál -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/label_material"
                            android:gravity="center"
                            style="@style/Text.Info"
                            android:layout_marginBottom="4dp"/>
                    </LinearLayout>

                    <LinearLayout
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp">

                        <Spinner
                            android:id="@+id/spinnerMaterial"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="12dp"/>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp">

        <!-- Typ filmu -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_film_type"
            android:gravity="center"
            style="@style/Text.Info"
            android:layout_marginBottom="1dp"/>

        <!-- Požadované zčernání -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_density"
            android:gravity="center"
            style="@style/Text.Info"
            android:layout_marginLeft="10dp"
            android:layout_marginBottom="1dp"/>

        <!-- Uživatelský koeficient -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_user_factor"
            android:gravity="center"
            style="@style/Text.Info"
            android:layout_marginBottom="1dp"/>
    </LinearLayout>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp">

        <Spinner
            android:id="@+id/spinnerFilmType"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginBottom="12dp" />

        <Spinner
            android:id="@+id/spinnerDensity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:layout_marginBottom="12dp" />

        <EditText
            android:id="@+id/inputUserFactor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.4"
            android:inputType="numberDecimal"
            android:gravity="center"
            android:layout_marginBottom="12dp"
            style="@style/Text.Input"/>
    </LinearLayout>

        <!-- Tloušťka materiálu / Weld reinforcement / Vzdálenost zářiče -->
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_thickness"
                android:gravity="center"
                style="@style/Text.Info" />
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_weld_reinforcement"
                android:gravity="center"
                style="@style/Text.Info"
                android:layout_marginStart="8dp"/>
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_distance"
                android:gravity="center"
                style="@style/Text.Info"
                android:layout_marginStart="8dp"/>
        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp">
            <EditText
                android:id="@+id/inputThickness"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:inputType="numberDecimal"
                android:gravity="center"
                style="@style/Text.Input"/>
            <EditText
                android:id="@+id/inputWeldReinforcement"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:inputType="numberDecimal"
                android:gravity="center"
                style="@style/Text.Input"
                android:layout_marginStart="8dp"/>
            <EditText
                android:id="@+id/inputDistance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:inputType="numberDecimal"
                android:gravity="center"
                style="@style/Text.Input"
                android:layout_marginStart="8dp"/>
        </LinearLayout>

        <!-- Varování o jednotkách -->
        <TextView
            android:id="@+id/textUnitWarning"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="⚠️ Zkontrolujte nastavení jednotek v Settings!"
            android:textSize="12sp"
            android:textColor="#FF9800"
            android:gravity="center"
            android:background="#FFF3E0"
            android:padding="4dp"
            android:layout_marginBottom="8dp"/>

        <!-- Typ objektu -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_object_type"
            android:gravity="center"
            style="@style/Text.Info"
            android:layout_marginBottom="4dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <Spinner
                android:id="@+id/spinnerObjectType"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/imageObjectTypeIcon"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginStart="8dp"
                android:contentDescription="@string/label_object_type"
                android:src="@drawable/ic_weld_sheet"
                android:scaleType="centerInside" />

        </LinearLayout>

        <!-- Barricade sekce - Titulek -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_barricade_section"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            style="@style/Text.Info"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp"/>

        <!-- Počet expozic / Dávka na bariéře / HVL kolimátoru -->
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_num_exposures"
                style="@style/Text.Info" />
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_barricade_dose"
                style="@style/Text.Info"
                android:layout_marginStart="8dp"/>
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:text="@string/label_hvl_collimator_type"
                style="@style/Text.Info"
                android:layout_marginStart="8dp"/>
        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">
            <EditText
                android:id="@+id/inputNumExposures"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:inputType="number"
                android:gravity="center"
                style="@style/Text.Input"/>
            <EditText
                android:id="@+id/inputBarricadeDose"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:inputType="numberDecimal"
                android:gravity="center"
                style="@style/Text.Input"
                android:layout_marginStart="8dp"/>

            <!-- Spinner pro výběr typu HVL -->
            <Spinner
                android:id="@+id/spinnerHvlType"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:layout_marginStart="8dp"/>
        </LinearLayout>

        <!-- Vypočítaná vzdálenost bariéry -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Vypočtená vzdálenost bariéry:"
            style="@style/Text.Info"
            android:layout_marginTop="8dp"/>
        <TextView
            android:id="@+id/textCalculatedBarricadeDistance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="--"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:background="#4CAF50"
            android:textColor="@android:color/white"
            android:padding="8dp"
            android:layout_marginBottom="16dp"/>

        <!-- Výsledek expozice -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/label_exposure_result"
            style="@style/Text.Info"
            android:layout_marginTop="16dp"/>
        <TextView
            android:id="@+id/textExposureResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="--"
            android:textSize="30sp"
            android:textStyle="bold"
            android:gravity="center"
            android:background="#525050"
            android:padding="8dp"
            android:layout_marginBottom="16dp"/>

        <!-- Stopky -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/label_stopwatch"
            style="@style/Text.Info" />
        <TextView
            android:id="@+id/textStopwatch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="00:00:00"
            android:textSize="60sp"
            android:gravity="center"
            android:layout_marginBottom="8dp"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">
            <Button
                android:id="@+id/btnStartStopwatch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_start" />
            <Button
                android:id="@+id/btnPauseStopwatch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_pause"
                android:layout_marginStart="8dp" />
            <Button
                android:id="@+id/btnResetStopwatch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_reset"
                android:layout_marginStart="8dp" />
        </LinearLayout>

        <!-- Historie bariéry -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/label_barricade_history"
            android:layout_marginTop="16dp"
            android:textStyle="bold" />
        <ListView
            android:id="@+id/listBarricadeHistory"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginTop="4dp"
            android:divider="@android:color/darker_gray"
            android:dividerHeight="1dp"/>
    </LinearLayout>
</ScrollView>
